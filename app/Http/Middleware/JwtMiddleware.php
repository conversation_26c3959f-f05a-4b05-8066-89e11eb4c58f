<?php

namespace App\Http\Middleware;

use Closure;
use <PERSON><PERSON>\JWTAuth\Facades\JWTAuth;
use <PERSON>mon\JWTAuth\Exceptions\JWTException;

class JwtMiddleware
{
    public function handle($request, Closure $next)
    {
        try {
            // Vérifier si l'utilisateur est authentifié avec un token valide
            $user = JWTAuth::parseToken()->authenticate();
            
            if (!$user) {
                return response()->json(['error' => 'Utilisateur non trouvé'], 404);
            }
            
            // Générer un nouveau token à chaque requête si nécessaire
            $newToken = JWTAuth::refresh();
            $response = $next($request);
            
            // Ajouter le nouveau token dans les headers de la réponse
            return $this->setAuthenticationHeader($response, $newToken);
            
        } catch (JWTException $e) {
            return response()->json(['error' => 'Token invalide ou expiré'], 401);
        }
    }
    
    protected function setAuthenticationHeader($response, $token)
    {
        $response->headers->set('Authorization', 'Bearer '.$token);
        return $response;
    }
}