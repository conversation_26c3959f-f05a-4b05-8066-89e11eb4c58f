<?php

namespace App\Http\Middleware;

use Closure;
use Ty<PERSON>\JWTAuth\Facades\JWTAuth;
use <PERSON>mon\JWTAuth\Exceptions\JWTException;

class JwtMiddleware
{
    public function handle($request, Closure $next)
    {
        try {
            // AJOUT AUGMENT AGENT : Vérification et authentification du token JWT
            $user = JWTAuth::parseToken()->authenticate();

            if (!$user) {
                // AJOUT AUGMENT AGENT : Gestion du cas où l'utilisateur n'existe plus
                return response()->json(['error' => 'Utilisateur non trouvé'], 404);
            }

            // AJOUT AUGMENT AGENT : Ajout de l'utilisateur authentifié à la requête
            $request->merge(['authenticated_user' => $user]);

            return $next($request);

        } catch (JWTException $e) {
            // AJOUT AUGMENT AGENT : Gestion des différents types d'erreurs JWT
            if ($e instanceof \Tymon\JWTAuth\Exceptions\TokenExpiredException) {
                return response()->json(['error' => 'Token expiré'], 401);
            } elseif ($e instanceof \Tymon\JWTAuth\Exceptions\TokenInvalidException) {
                return response()->json(['error' => 'Token invalide'], 401);
            } elseif ($e instanceof \Tymon\JWTAuth\Exceptions\JWTException) {
                return response()->json(['error' => 'Token manquant'], 401);
            }

            return response()->json(['error' => 'Erreur d\'authentification'], 401);
        }
    }
}