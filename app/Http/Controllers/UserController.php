<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
// AJOUT AUGMENT AGENT : Import des facades JWT pour l'authentification par token
use <PERSON><PERSON>\JWTAuth\Facades\JWTAuth;
use <PERSON><PERSON>\JWTAuth\Exceptions\JWTException;

class UserController
{
    /**P
     * Display a listing of the resource.
     */
    public function index()
    {
        // AJOUT AUGMENT AGENT : API - Retourner tous les utilisateurs en JSON
        $users = User::all();
        return response()->json([
            'success' => true,
            'data' => $users
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        // AJOUT AUGMENT AGENT : API - Plus besoin de vue, retourner un message
        return response()->json([
            'message' => 'Utilisez POST /api/users pour créer un utilisateur'
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        // AJOUT AUGMENT AGENT : API - Validation et création d'utilisateur avec réponse JSON
        $validateData = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email:dns|unique:users',
            'password' => 'required|min:5|max:255'
        ]);

        $validateData['password'] = Hash::make($validateData['password']);
        $user = User::create($validateData);

        // AJOUT AUGMENT AGENT : API - Retourner l'utilisateur créé en JSON
        return response()->json([
            'success' => true,
            'message' => 'Inscription réussie !',
            'data' => $user
        ], 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        // AJOUT AUGMENT AGENT : API - Afficher un utilisateur spécifique
        $user = User::find($id);

        if (!$user) {
            return response()->json([
                'success' => false,
                'message' => 'Utilisateur non trouvé'
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => $user
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        // AJOUT AUGMENT AGENT : API - Plus besoin de vue, retourner un message
        return response()->json([
            'message' => 'Utilisez PUT /api/users/{id} pour modifier un utilisateur'
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        // AJOUT AUGMENT AGENT : API - Mise à jour d'un utilisateur
        $user = User::find($id);

        if (!$user) {
            return response()->json([
                'success' => false,
                'message' => 'Utilisateur non trouvé'
            ], 404);
        }

        $validateData = $request->validate([
            'name' => 'sometimes|required|string|max:255',
            'email' => 'sometimes|required|email:dns|unique:users,email,' . $id,
            'password' => 'sometimes|required|min:5|max:255'
        ]);

        if (isset($validateData['password'])) {
            $validateData['password'] = Hash::make($validateData['password']);
        }

        $user->update($validateData);

        return response()->json([
            'success' => true,
            'message' => 'Utilisateur mis à jour avec succès',
            'data' => $user
        ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        // AJOUT AUGMENT AGENT : API - Suppression d'un utilisateur
        $user = User::find($id);

        if (!$user) {
            return response()->json([
                'success' => false,
                'message' => 'Utilisateur non trouvé'
            ], 404);
        }

        $user->delete();

        return response()->json([
            'success' => true,
            'message' => 'Utilisateur supprimé avec succès'
        ]);
    }
    public function showLoginForm(){
        // AJOUT AUGMENT AGENT : API - Plus besoin de vue, retourner un message
        return response()->json([
            'message' => 'Utilisez POST /api/login pour vous connecter',
            'required_fields' => ['email', 'password']
        ]);
    }
    public function login(Request $request){
        // AJOUT AUGMENT AGENT : Validation des données de connexion
        $request->validate([
            'email' => 'required|email',
            'password' => 'required|min:5'
        ]);

        $user = User::where('email', $request->email)->first();

        if (!$user || !Hash::check($request->password, $user->password)) {
            // AJOUT AUGMENT AGENT : API - Réponse JSON pour erreur de connexion
            return response()->json([
                'success' => false,
                'message' => 'Email ou mot de passe incorrect'
            ], 401);
        }

        // AJOUT AUGMENT AGENT : API - Génération du token JWT après connexion réussie
        try {
            $token = JWTAuth::fromUser($user);

            // AJOUT AUGMENT AGENT : API - Retour du token et des informations utilisateur
            return response()->json([
                'success' => true,
                'message' => 'Connexion réussie',
                'token' => $token,
                'user' => [
                    'id' => $user->id,
                    'name' => $user->name,
                    'email' => $user->email
                ]
            ]);

        } catch (JWTException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Impossible de créer le token'
            ], 500);
        }
    }

    // AJOUT AUGMENT AGENT : API - Méthode pour la déconnexion avec invalidation du token JWT
    public function logout(Request $request)
    {
        try {
            // AJOUT AUGMENT AGENT : API - Invalidation du token JWT
            JWTAuth::invalidate(JWTAuth::getToken());

            // AJOUT AUGMENT AGENT : API - Réponse JSON de déconnexion
            return response()->json([
                'success' => true,
                'message' => 'Déconnexion réussie'
            ]);

        } catch (JWTException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erreur lors de la déconnexion'
            ], 500);
        }
    }

    // AJOUT AUGMENT AGENT : Méthode pour obtenir l'utilisateur authentifié via JWT
    public function getAuthenticatedUser()
    {
        try {
            if (!$user = JWTAuth::parseToken()->authenticate()) {
                return response()->json(['error' => 'Utilisateur non trouvé'], 404);
            }
        } catch (JWTException $e) {
            return response()->json(['error' => 'Token invalide'], 400);
        }

        return response()->json(['user' => $user]);
    }
}
