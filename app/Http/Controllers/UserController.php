<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
// AJOUT AUGMENT AGENT : Import des facades JWT pour l'authentification par token
use <PERSON><PERSON>\JWTAuth\Facades\JWTAuth;
use <PERSON><PERSON>\JWTAuth\Exceptions\JWTException;

class UserController
{
    /**P
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
        return view('users.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
        $validateData=$request->validate([
            'name'=>'required',
            'email'=>'required|email:dns|unique:users',
            'password'=>'required|min:5|max:255'
        ]);
        $validateData['password']=Hash::make($validateData['password']);
        User::create($validateData);
         return response()->json(['message' => 'Inscription réussie'], 201);
        }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }
    public function showLoginForm(){
        return view('users.login');
    }
    public function login(Request $request){
        // AJOUT AUGMENT AGENT : Validation des données de connexion
        $request->validate([
            'email' => 'required|email',
            'password' => 'required|min:5'
        ]);

        $user=User::Where('email',$request->email)->first();
        if(!$user || !Hash::check($request->password,$user->password)){
            // return redirect('/login')->with('error','Login Failed');
            return response()->json(['error' => 'Login Failed'], 401);
        }

        // AJOUT AUGMENT AGENT : Génération du token JWT après connexion réussie
        try {
            $token = JWTAuth::fromUser($user);

            // AJOUT AUGMENT AGENT : Retour du token en JSON pour les API ou redirection pour le web
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Login Success',
                    'token' => $token,
                    'user' => $user
                ]);
            }

            // AJOUT AUGMENT AGENT : Pour les requêtes web, on stocke le token en session
            session(['jwt_token' => $token]);
            // return back()->with('success', 'Login Success')->with('token', $token);
            return response()->json(['message' => 'Login Success', 'token' => $token]);

        } catch (JWTException $e) {
            return response()->json(['error' => 'Impossible de créer le token'], 500);
        }
    }

    // AJOUT AUGMENT AGENT : Méthode pour la déconnexion avec invalidation du token JWT
    public function logout(Request $request)
    {
        try {
            // AJOUT AUGMENT AGENT : Invalidation du token JWT
            JWTAuth::invalidate(JWTAuth::getToken());

            // AJOUT AUGMENT AGENT : Suppression du token de la session
            session()->forget('jwt_token');

            if ($request->expectsJson()) {
                return response()->json(['message' => 'Déconnexion réussie']);
            }

            return redirect('/login')->with('success', 'Déconnexion réussie');

        } catch (JWTException $e) {
            return response()->json(['error' => 'Erreur lors de la déconnexion'], 500);
        }
    }

    // AJOUT AUGMENT AGENT : Méthode pour obtenir l'utilisateur authentifié via JWT
    public function getAuthenticatedUser()
    {
        try {
            if (!$user = JWTAuth::parseToken()->authenticate()) {
                return response()->json(['error' => 'Utilisateur non trouvé'], 404);
            }
        } catch (JWTException $e) {
            return response()->json(['error' => 'Token invalide'], 400);
        }

        return response()->json(['user' => $user]);
    }
}
