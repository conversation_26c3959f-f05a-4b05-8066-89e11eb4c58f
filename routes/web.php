<?php

use App\Http\Controllers\UserController;
use Illuminate\Support\Facades\Route;

Route::get('/', function () {
    // AJOUT AUGMENT AGENT : API - Page d'accueil avec informations API
    return response()->json([
        'message' => 'Bienvenue sur l\'API Laravel',
        'version' => '1.0',
        'endpoints' => [
            'POST /api/register' => 'Inscription',
            'POST /api/login' => 'Connexion',
            'GET /api/users' => 'Liste des utilisateurs (JWT requis)',
            'GET /api/users/{id}' => 'Détails utilisateur (JWT requis)',
            'PUT /api/users/{id}' => 'Modifier utilisateur (JWT requis)',
            'DELETE /api/users/{id}' => 'Supprimer utilisateur (JWT requis)',
            'POST /api/logout' => 'Déconnexion (JWT requis)',
            'GET /api/me' => 'Profil utilisateur (JWT requis)'
        ]
    ]);
});

// AJOUT AUGMENT AGENT : API - Routes publiques (sans authentification)
Route::prefix('api')->group(function () {
    Route::post('/register', [UserController::class, 'store'])->name('api.register');
    Route::post('/login', [UserController::class, 'login'])->name('api.login');

    // AJOUT AUGMENT AGENT : API - Routes protégées par le middleware JWT
    Route::middleware(['jwt.auth'])->group(function () {
        // AJOUT AUGMENT AGENT : API - CRUD utilisateurs
        Route::get('/users', [UserController::class, 'index'])->name('api.users.index');
        Route::get('/users/{id}', [UserController::class, 'show'])->name('api.users.show');
        Route::put('/users/{id}', [UserController::class, 'update'])->name('api.users.update');
        Route::delete('/users/{id}', [UserController::class, 'destroy'])->name('api.users.destroy');

        // AJOUT AUGMENT AGENT : API - Authentification
        Route::post('/logout', [UserController::class, 'logout'])->name('api.logout');
        Route::get('/me', [UserController::class, 'getAuthenticatedUser'])->name('api.user.profile');
    });
});