<?php

use App\Http\Controllers\UserController;
use Illuminate\Support\Facades\Route;

Route::get('/', function () {
    return view('welcome');
});

// AJOUT AUGMENT AGENT : Routes publiques (sans authentification)
Route::get('/login', [UserController::class, 'showLoginForm'])->name('login.form');
Route::post('/login', [UserController::class, 'login'])->name('login');
Route::get('/users/create', [UserController::class, 'create'])->name('users.create');
Route::post('/users', [UserController::class, 'store'])->name('users.store');

// AJOUT AUGMENT AGENT : Routes protégées par le middleware JWT
Route::middleware(['jwt.auth'])->group(function () {
    // AJOUT AUGMENT AGENT : Routes pour les utilisateurs authentifiés
    Route::get('/users', [UserController::class, 'index'])->name('users.index');
    Route::get('/users/{id}', [UserController::class, 'show'])->name('users.show');
    Route::get('/users/{id}/edit', [UserController::class, 'edit'])->name('users.edit');
    Route::put('/users/{id}', [UserController::class, 'update'])->name('users.update');
    Route::delete('/users/{id}', [UserController::class, 'destroy'])->name('users.destroy');

    // AJOUT AUGMENT AGENT : Routes pour la déconnexion et profil utilisateur
    Route::post('/logout', [UserController::class, 'logout'])->name('logout');
    Route::get('/me', [UserController::class, 'getAuthenticatedUser'])->name('user.profile');
});