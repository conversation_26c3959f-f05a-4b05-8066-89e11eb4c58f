<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>Document</title>
</head>
<body>
<h1>Inscription</h1>

<form method="POST" action="{{ route('users.store') }}">
    @csrf

    Name: <input type="text" name="name" id="name"><br>
    Email: <input type="email" name="email" id="email"><br>
    Password: <input type="password" name="password" id="password"><br>
    Confirme Password: <input type="password" name="password_confirmation" id="password_confirmation"><br>

    <button type="submit">Inscription</button>
</form>


@if ($errors->any())
    <div style="color: red;">
        <ul>
            @foreach ($errors->all() as $error)
                <li>{{ $error }}</li>
            @endforeach
        </ul>
    </div>
@endif

    
</body>

</html>